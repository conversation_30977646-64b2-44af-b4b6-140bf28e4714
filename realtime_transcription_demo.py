#!/usr/bin/env python3
"""
RealtimeSTT Live Transcription Demo with Ollama

This script demonstrates real-time speech transcription where the app
continuously listens and transcribes your speech as you talk, just like
in the demo videos.

Features:
- Real-time transcription while you speak
- Live updates as words are being spoken
- Uses local Ollama models (no API keys needed)
- Automatic fallback to Whisper if Ollama unavailable
- Voice activity detection
- Clean, formatted output

Requirements:
- Ollama installed and running (ollama serve)
- gemma3:1b model downloaded (ollama pull gemma3:1b)
- Working microphone
"""

import sys
import os
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    print("Please install: pip install RealtimeSTT")
    sys.exit(1)

def check_ollama_status():
    """Check if Ollama is running and has the required model."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [m['name'] for m in models]
            print(f"✓ Ollama is running with {len(models)} models")
            
            if any('gemma3:1b' in name for name in model_names):
                print("✓ gemma3:1b model is available")
                return True
            else:
                print("⚠ gemma3:1b model not found")
                print("Run: ollama pull gemma3:1b")
                return False
        else:
            print(f"✗ Ollama server error: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Ollama not accessible: {e}")
        print("Make sure to run: ollama serve")
        return False

def on_realtime_update(text):
    """Callback for real-time transcription updates."""
    # Clear the line and print the current transcription
    print(f"\r🎤 Live: {text}", end="", flush=True)

def on_final_transcription(text):
    """Callback for final transcription result."""
    if text.strip():
        print(f"\n✅ Final: {text}")
        print("-" * 50)

def on_recording_start():
    """Callback when recording starts."""
    print("🔴 Recording started...")

def on_recording_stop():
    """Callback when recording stops."""
    print("\n⏹️  Recording stopped, processing...")

def main():
    print("=" * 60)
    print("RealtimeSTT Live Transcription Demo")
    print("=" * 60)
    print("This demo shows real-time speech transcription")
    print("You'll see live updates as you speak!")
    print()
    
    # Check Ollama status
    ollama_available = check_ollama_status()
    print()
    
    if not ollama_available:
        print("Continuing with Whisper fallback...")
        print()
    
    # Configuration for real-time transcription
    config = {
        # Core settings
        'model': 'base',  # Fallback Whisper model
        'language': 'en',
        'use_microphone': True,
        'spinner': False,  # Disable spinner for cleaner output
        
        # Ollama integration
        'use_ollama': True,
        'ollama_model': 'gemma3:1b',
        'ollama_host': 'localhost:11434',
        'ollama_timeout': 30.0,
        'fallback_to_external': True,
        
        # Real-time transcription (this is the key!)
        'enable_realtime_transcription': True,
        'realtime_model_type': 'tiny.en',  # Fast model for real-time
        'realtime_processing_pause': 0.1,  # How often to update (seconds)
        'use_main_model_for_realtime': False,  # Use separate fast model
        
        # Voice activity detection
        'silero_sensitivity': 0.4,
        'webrtc_sensitivity': 3,
        'post_speech_silence_duration': 1.0,  # Wait 1 sec after speech stops
        'min_length_of_recording': 0.5,
        'min_gap_between_recordings': 0.5,
        
        # Performance settings
        'beam_size': 5,
        'beam_size_realtime': 3,  # Faster beam search for real-time
        'batch_size': 16,
        'realtime_batch_size': 8,  # Smaller batches for real-time
        
        # Callbacks for live updates
        'on_realtime_transcription_update': on_realtime_update,
        'on_recording_start': on_recording_start,
        'on_recording_stop': on_recording_stop,
    }
    
    print("Initializing RealtimeSTT...")
    print("Configuration:")
    print(f"  Real-time transcription: {config['enable_realtime_transcription']}")
    print(f"  Real-time model: {config['realtime_model_type']}")
    print(f"  Update interval: {config['realtime_processing_pause']}s")
    print(f"  Ollama model: {config['ollama_model']}")
    print(f"  Fallback model: {config['model']}")
    print()
    
    try:
        # Initialize the recorder
        recorder = AudioToTextRecorder(**config)
        
        # Check which transcription engine is being used
        if hasattr(recorder, 'is_ollama_available') and recorder.is_ollama_available():
            print("✓ Using Ollama for transcription")
        else:
            print("⚠ Using Whisper fallback")
        
        print()
        print("=" * 60)
        print("🎤 READY FOR LIVE TRANSCRIPTION!")
        print("=" * 60)
        print("Start speaking - you'll see live updates as you talk")
        print("Press Ctrl+C to stop")
        print("=" * 60)
        print()
        
        # Main transcription loop
        sentence_count = 0
        while True:
            try:
                # Get final transcription (this will show real-time updates via callback)
                final_text = recorder.text()
                
                if final_text.strip():
                    sentence_count += 1
                    print(f"[{sentence_count:03d}] {final_text}")
                    print()
                    
            except KeyboardInterrupt:
                print("\n\n🛑 Stopping transcription...")
                break
            except Exception as e:
                print(f"\n❌ Error during transcription: {e}")
                print("Continuing...")
                continue
    
    except Exception as e:
        print(f"❌ Failed to initialize recorder: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Download the model: ollama pull gemma3:1b")
        print("3. Check microphone permissions")
        print("4. Try running: python tests/simple_test.py")
        return 1
    
    finally:
        # Cleanup
        if 'recorder' in locals():
            try:
                recorder.shutdown()
                print("✓ Recorder shutdown complete")
            except:
                pass
    
    print("\n🎉 Live transcription demo completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
