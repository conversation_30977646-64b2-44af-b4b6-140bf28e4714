#!/usr/bin/env python3
"""
Dual Mode Transcription with Ollama

Simple transcription tool with two modes:
1. Microphone mode - For dictation during work
2. System audio mode - For meetings, videos, podcasts transcription

Uses only Ollama gemma3:1b model (no fallbacks).
"""

import sys
import os
import time
import warnings
import threading
import queue
from pathlib import Path

# Suppress configuration warnings
warnings.filterwarnings("ignore", message="Configuration warning:*")

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    print("Please install: pip install RealtimeSTT")
    sys.exit(1)

def check_requirements():
    """Check if all requirements are available."""
    # Check PyAudioWPatch for system audio
    try:
        import pyaudiowpatch
        print("✓ PyAudioWPatch available - System audio supported")
        pyaudio_ok = True
    except ImportError:
        print("✗ PyAudioWPatch not found - System audio mode unavailable")
        print("Install with: pip install PyAudioWPatch")
        pyaudio_ok = False
    
    # Check Ollama
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [m['name'] for m in models]
            
            if any('gemma3:1b' in name for name in model_names):
                print("✓ Ollama gemma3:1b model available")
                ollama_ok = True
            else:
                print("✗ gemma3:1b model not found")
                print("Run: ollama pull gemma3:1b")
                ollama_ok = False
        else:
            print(f"✗ Ollama server error: {response.status_code}")
            ollama_ok = False
    except Exception as e:
        print(f"✗ Ollama not accessible: {e}")
        print("Make sure to run: ollama serve")
        ollama_ok = False
    
    return pyaudio_ok, ollama_ok

def create_wake_word_recorder():
    """Create a lightweight recorder for wake word detection."""
    return AudioToTextRecorder(
        model='tiny.en',  # Fast model for wake word detection
        language='en',
        use_microphone=True,
        use_system_audio=False,
        spinner=False,
        # Aggressive settings for quick wake word detection
        silero_sensitivity=0.2,  # More sensitive
        webrtc_sensitivity=1,    # More sensitive
        post_speech_silence_duration=0.5,  # Shorter silence
        min_length_of_recording=0.3,       # Shorter recordings
        min_gap_between_recordings=0.1,    # Faster response
        beam_size=1,  # Faster processing
        batch_size=8,
    )

def detect_wake_word_in_text(text, wake_word="boss"):
    """Check if wake word is present in transcribed text."""
    if not text:
        return False

    # Clean and normalize text
    words = text.lower().strip().split()
    return wake_word.lower() in words

def listen_for_wake_word():
    """Listen for wake word and return True when detected."""
    print(f"🔍 Listening for wake word 'boss'...")
    print("Say 'boss' followed by your dictation")
    print("Press Ctrl+C to stop")
    print("-" * 60)

    try:
        print("Initializing wake word detector...")
        recorder = create_wake_word_recorder()
        print("✓ Wake word detector ready")

        while True:
            try:
                # Get transcription from wake word detector
                text = recorder.text()

                if text and detect_wake_word_in_text(text):
                    print(f"\n🎯 Wake word detected in: '{text}'")
                    print("📝 Listening for dictation...")
                    recorder.shutdown()
                    return True

            except KeyboardInterrupt:
                print("\n🛑 Stopping wake word detection...")
                break
            except Exception as e:
                print(f"⚠ Wake word detection error: {e}")
                continue

    except Exception as e:
        print(f"❌ Error in wake word detection: {e}")
        return False
    finally:
        if 'recorder' in locals():
            try:
                recorder.shutdown()
            except:
                pass

    return False

def get_base_config():
    """Get base configuration for Ollama transcription."""
    return {
        # Whisper model (required but not used when Ollama is primary)
        'model': 'tiny.en',
        'language': 'en',
        'spinner': False,

        # Ollama configuration (primary transcription)
        'use_ollama': True,
        'ollama_model': 'gemma3:1b',
        'ollama_host': 'localhost:11434',
        'ollama_timeout': 30.0,

        # Performance settings
        'beam_size': 5,
        'batch_size': 16,

        # Voice activity detection
        'silero_sensitivity': 0.4,
        'webrtc_sensitivity': 3,
        'post_speech_silence_duration': 1.0,
        'min_length_of_recording': 0.5,
        'min_gap_between_recordings': 0.5,
    }

def create_microphone_recorder():
    """Create recorder for microphone mode (dictation)."""
    config = get_base_config()
    config.update({
        'use_microphone': True,
        'use_system_audio': False,
    })
    return AudioToTextRecorder(**config)

def create_system_audio_recorder():
    """Create recorder for system audio mode (meetings, videos)."""
    config = get_base_config()
    config.update({
        'use_microphone': False,
        'use_system_audio': True,
        'system_audio_channels': 1,
        'system_audio_device_index': None,
        # Adjust sensitivity for system audio
        'silero_sensitivity': 0.3,
        'webrtc_sensitivity': 2,
        'post_speech_silence_duration': 1.5,
    })
    return AudioToTextRecorder(**config)

def microphone_mode():
    """Microphone transcription mode with wake word detection."""
    print("\n" + "=" * 60)
    print("🎤 MICROPHONE MODE - Wake Word Dictation")
    print("=" * 60)
    print("Choose mode:")
    print("1. Wake word mode (say 'boss' then dictate)")
    print("2. Continuous mode (always transcribing)")
    print()

    mode_choice = input("Enter choice (1/2): ").strip()

    if mode_choice == "1":
        return wake_word_microphone_mode()
    elif mode_choice == "2":
        return continuous_microphone_mode()
    else:
        print("Invalid choice. Returning to main menu.")
        return True

def wake_word_microphone_mode():
    """Wake word activated microphone mode."""
    print("\n" + "=" * 60)
    print("🎯 WAKE WORD MODE - Say 'boss' then dictate")
    print("=" * 60)

    sentence_count = 0

    try:
        while True:
            # Listen for wake word
            wake_detected = listen_for_wake_word()

            if wake_detected:
                # Wake word detected, start transcription
                try:
                    print("Initializing dictation recorder...")
                    recorder = create_microphone_recorder()
                    print("✓ Ready for dictation")

                    # Get the dictation that follows the wake word
                    text = recorder.text()

                    if text.strip():
                        sentence_count += 1
                        timestamp = time.strftime("%H:%M:%S")

                        # Remove wake word from beginning if present
                        cleaned_text = text.lower().replace("boss", "", 1).strip()
                        if not cleaned_text:
                            cleaned_text = text.strip()

                        print(f"\n✅ [{sentence_count:03d}] [{timestamp}] {cleaned_text}")
                        print("\n" + "🔍 Listening for wake word 'boss' again...")
                        print("-" * 60)
                    else:
                        print("⚠ No dictation detected after wake word")
                        print("🔍 Listening for wake word 'boss' again...")
                        print("-" * 60)

                    recorder.shutdown()

                except Exception as e:
                    print(f"❌ Error during dictation: {e}")
                    print("🔍 Returning to wake word listening...")
                    if 'recorder' in locals():
                        try:
                            recorder.shutdown()
                        except:
                            pass
                    continue
            else:
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping wake word mode...")
    except Exception as e:
        print(f"❌ Error in wake word mode: {e}")
        return False

    return True

def continuous_microphone_mode():
    """Continuous microphone transcription mode."""
    print("\n" + "=" * 60)
    print("🎤 CONTINUOUS MODE - Always transcribing")
    print("=" * 60)
    print("Speak into your microphone for transcription")
    print("Press Ctrl+C to stop")
    print("-" * 60)

    try:
        recorder = create_microphone_recorder()
        print("✓ Microphone recorder initialized")
        print()

        sentence_count = 0
        while True:
            try:
                text = recorder.text()
                if text.strip():
                    sentence_count += 1
                    timestamp = time.strftime("%H:%M:%S")
                    print(f"[{sentence_count:03d}] [{timestamp}] {text}")
                    print()

            except KeyboardInterrupt:
                print("\n🛑 Stopping continuous mode...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue

    except Exception as e:
        print(f"❌ Failed to initialize microphone recorder: {e}")
        return False
    finally:
        if 'recorder' in locals():
            recorder.shutdown()

    return True

def system_audio_mode():
    """System audio transcription mode for meetings/videos."""
    print("\n" + "=" * 60)
    print("🔊 SYSTEM AUDIO MODE - Meetings/Videos")
    print("=" * 60)
    print("Play audio (meetings, videos, podcasts) for transcription")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        recorder = create_system_audio_recorder()
        print("✓ System audio recorder initialized")
        
        # Show available devices
        try:
            devices = recorder.get_available_system_audio_devices()
            print(f"✓ Using system audio device: {devices[0]['name'] if devices else 'Default'}")
        except:
            print("✓ Using default system audio device")
        print()
        
        sentence_count = 0
        while True:
            try:
                text = recorder.text()
                if text.strip():
                    sentence_count += 1
                    timestamp = time.strftime("%H:%M:%S")
                    print(f"[{sentence_count:03d}] [{timestamp}] {text}")
                    print()
                    
            except KeyboardInterrupt:
                print("\n🛑 Stopping system audio mode...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
                
    except Exception as e:
        print(f"❌ Failed to initialize system audio recorder: {e}")
        return False
    finally:
        if 'recorder' in locals():
            recorder.shutdown()
    
    return True

def main():
    print("=" * 60)
    print("Dual Mode Transcription with Ollama")
    print("=" * 60)
    print("Two modes available:")
    print("1. Microphone mode - For dictation during work")
    print("2. System audio mode - For meetings, videos, podcasts")
    print()
    
    # Check requirements
    pyaudio_ok, ollama_ok = check_requirements()
    print()
    
    if not ollama_ok:
        print("❌ Ollama gemma3:1b is required for transcription")
        print("1. Start Ollama: ollama serve")
        print("2. Download model: ollama pull gemma3:1b")
        return 1
    
    # Mode selection
    while True:
        print("Select mode:")
        print("1. Microphone mode (dictation)")
        if pyaudio_ok:
            print("2. System audio mode (meetings/videos)")
        else:
            print("2. System audio mode (UNAVAILABLE - install PyAudioWPatch)")
        print("q. Quit")
        
        choice = input("\nEnter choice (1/2/q): ").strip().lower()
        
        if choice == 'q':
            print("Goodbye!")
            break
        elif choice == '1':
            microphone_mode()
        elif choice == '2':
            if pyaudio_ok:
                system_audio_mode()
            else:
                print("❌ System audio mode requires PyAudioWPatch")
                print("Install with: pip install PyAudioWPatch")
        else:
            print("Invalid choice. Please enter 1, 2, or q")
        
        print("\n" + "=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
