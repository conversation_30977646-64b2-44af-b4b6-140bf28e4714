#!/usr/bin/env python3
"""
Dual Mode Transcription with Ollama

Simple transcription tool with two modes:
1. Microphone mode - For dictation during work
2. System audio mode - For meetings, videos, podcasts transcription

Uses only Ollama gemma3:1b model (no fallbacks).
"""

import sys
import os
import time
import warnings
from pathlib import Path

# Suppress configuration warnings
warnings.filterwarnings("ignore", message="Configuration warning:*")

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    print("Please install: pip install RealtimeSTT")
    sys.exit(1)

def check_requirements():
    """Check if all requirements are available."""
    # Check PyAudioWPatch for system audio
    try:
        import pyaudiowpatch
        print("✓ PyAudioWPatch available - System audio supported")
        pyaudio_ok = True
    except ImportError:
        print("✗ PyAudioWPatch not found - System audio mode unavailable")
        print("Install with: pip install PyAudioWPatch")
        pyaudio_ok = False
    
    # Check Ollama
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [m['name'] for m in models]
            
            if any('gemma3:1b' in name for name in model_names):
                print("✓ Ollama gemma3:1b model available")
                ollama_ok = True
            else:
                print("✗ gemma3:1b model not found")
                print("Run: ollama pull gemma3:1b")
                ollama_ok = False
        else:
            print(f"✗ Ollama server error: {response.status_code}")
            ollama_ok = False
    except Exception as e:
        print(f"✗ Ollama not accessible: {e}")
        print("Make sure to run: ollama serve")
        ollama_ok = False
    
    return pyaudio_ok, ollama_ok

def get_base_config():
    """Get base configuration for Ollama transcription."""
    return {
        # Whisper model (required but not used when Ollama is primary)
        'model': 'tiny.en',
        'language': 'en',
        'spinner': False,

        # Ollama configuration (primary transcription)
        'use_ollama': True,
        'ollama_model': 'gemma3:1b',
        'ollama_host': 'localhost:11434',
        'ollama_timeout': 30.0,

        # Performance settings
        'beam_size': 5,
        'batch_size': 16,

        # Voice activity detection
        'silero_sensitivity': 0.4,
        'webrtc_sensitivity': 3,
        'post_speech_silence_duration': 1.0,
        'min_length_of_recording': 0.5,
        'min_gap_between_recordings': 0.5,
    }

def create_microphone_recorder():
    """Create recorder for microphone mode (dictation)."""
    config = get_base_config()
    config.update({
        'use_microphone': True,
        'use_system_audio': False,
    })
    return AudioToTextRecorder(**config)

def create_system_audio_recorder():
    """Create recorder for system audio mode (meetings, videos)."""
    config = get_base_config()
    config.update({
        'use_microphone': False,
        'use_system_audio': True,
        'system_audio_channels': 1,
        'system_audio_device_index': None,
        # Adjust sensitivity for system audio
        'silero_sensitivity': 0.3,
        'webrtc_sensitivity': 2,
        'post_speech_silence_duration': 1.5,
    })
    return AudioToTextRecorder(**config)

def microphone_mode():
    """Microphone transcription mode for dictation."""
    print("\n" + "=" * 60)
    print("🎤 MICROPHONE MODE - Dictation")
    print("=" * 60)
    print("Speak into your microphone for transcription")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        recorder = create_microphone_recorder()
        print("✓ Microphone recorder initialized")
        print()
        
        sentence_count = 0
        while True:
            try:
                text = recorder.text()
                if text.strip():
                    sentence_count += 1
                    timestamp = time.strftime("%H:%M:%S")
                    print(f"[{sentence_count:03d}] [{timestamp}] {text}")
                    print()
                    
            except KeyboardInterrupt:
                print("\n🛑 Stopping microphone mode...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
                
    except Exception as e:
        print(f"❌ Failed to initialize microphone recorder: {e}")
        return False
    finally:
        if 'recorder' in locals():
            recorder.shutdown()
    
    return True

def system_audio_mode():
    """System audio transcription mode for meetings/videos."""
    print("\n" + "=" * 60)
    print("🔊 SYSTEM AUDIO MODE - Meetings/Videos")
    print("=" * 60)
    print("Play audio (meetings, videos, podcasts) for transcription")
    print("Press Ctrl+C to stop")
    print("-" * 60)
    
    try:
        recorder = create_system_audio_recorder()
        print("✓ System audio recorder initialized")
        
        # Show available devices
        try:
            devices = recorder.get_available_system_audio_devices()
            print(f"✓ Using system audio device: {devices[0]['name'] if devices else 'Default'}")
        except:
            print("✓ Using default system audio device")
        print()
        
        sentence_count = 0
        while True:
            try:
                text = recorder.text()
                if text.strip():
                    sentence_count += 1
                    timestamp = time.strftime("%H:%M:%S")
                    print(f"[{sentence_count:03d}] [{timestamp}] {text}")
                    print()
                    
            except KeyboardInterrupt:
                print("\n🛑 Stopping system audio mode...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
                
    except Exception as e:
        print(f"❌ Failed to initialize system audio recorder: {e}")
        return False
    finally:
        if 'recorder' in locals():
            recorder.shutdown()
    
    return True

def main():
    print("=" * 60)
    print("Dual Mode Transcription with Ollama")
    print("=" * 60)
    print("Two modes available:")
    print("1. Microphone mode - For dictation during work")
    print("2. System audio mode - For meetings, videos, podcasts")
    print()
    
    # Check requirements
    pyaudio_ok, ollama_ok = check_requirements()
    print()
    
    if not ollama_ok:
        print("❌ Ollama gemma3:1b is required for transcription")
        print("1. Start Ollama: ollama serve")
        print("2. Download model: ollama pull gemma3:1b")
        return 1
    
    # Mode selection
    while True:
        print("Select mode:")
        print("1. Microphone mode (dictation)")
        if pyaudio_ok:
            print("2. System audio mode (meetings/videos)")
        else:
            print("2. System audio mode (UNAVAILABLE - install PyAudioWPatch)")
        print("q. Quit")
        
        choice = input("\nEnter choice (1/2/q): ").strip().lower()
        
        if choice == 'q':
            print("Goodbye!")
            break
        elif choice == '1':
            microphone_mode()
        elif choice == '2':
            if pyaudio_ok:
                system_audio_mode()
            else:
                print("❌ System audio mode requires PyAudioWPatch")
                print("Install with: pip install PyAudioWPatch")
        else:
            print("Invalid choice. Please enter 1, 2, or q")
        
        print("\n" + "=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
